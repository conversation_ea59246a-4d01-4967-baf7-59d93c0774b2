package com.maguo.loan.cash.flow.entity;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDate;

@Entity
@Table(name = "batch_task_monitoring")
public class BatchTaskMonitoring extends BaseEntity {
    private String taskHandler;
    private String taskDescription;
    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;
    private String loanId;
    private String resultsExecution;
    private String reccType;
    private LocalDate taskStartTime;
    private LocalDate taskEndTime;
    public String getTaskHandler() {
        return taskHandler;
    }

    public void setTaskHandler(String taskHandler) {
        this.taskHandler = taskHandler;
    }

    public String getTaskDescription() {
        return taskDescription;
    }

    public void setTaskDescription(String taskDescription) {
        this.taskDescription = taskDescription;
    }

    public String getResultsExecution() {
        return resultsExecution;
    }

    public void setResultsExecution(String resultsExecution) {
        this.resultsExecution = resultsExecution;
    }

    public String getReccType() {
        return reccType;
    }

    public void setReccType(String reccType) {
        this.reccType = reccType;
    }

    public LocalDate getTaskStartTime() {
        return taskStartTime;
    }

    public void setTaskStartTime(LocalDate taskStartTime) {
        this.taskStartTime = taskStartTime;
    }

    public LocalDate getTaskEndTime() {
        return taskEndTime;
    }

    public void setTaskEndTime(LocalDate taskEndTime) {
        this.taskEndTime = taskEndTime;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    @Override
    protected String prefix() {
        return "BT";
    }
}

package com.maguo.loan.cash.flow.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum JobEnums {
    repayAfterJhJobHXBK("repayAfterJhJob", "还款后还款计划-绿信-湖消"),
    repayDetailJhJobHXBK("repayDetailJhJob", "还款明细文件-绿信-湖消"),
    repayPlanJhJobHXBK("repayPlanJhJob", "还款计划文件-绿信-湖消"),
    loanJhJobHXBK("loanJhJob", "放款明细文件-绿信-湖消"),
    ppdLoanJob("ppdLoanJob", "放款文件-跑批-拍拍"),
    ppdRepayJob("ppdRepayJob", "还款文件-跑批-拍拍"),
    ppdSignJob("ppdSignJob", "协议文件上传-拍拍"),
    ppdClearDownloadJob("ppdClearDownloadJob", "结清证明-拍拍"),
    repayAfterJhjobCYBK("repayAfterJhjobCYBK", "还款后还款计划-绿信-长银"),
    repayDetailJhJobCYBK("repayDetailJhJobCYBK", "还款后还款计划-绿信-长银"),
    repayPlanJhJobCYBK("repayPlanJhJobCYBK", "还款计划文件-绿信-长银"),
    loanJhJobCYBK("loanJhJobCYBK", "放款明细文件-绿信-长银"),
    clearVoucherDownloanJob("clearVoucherDownloanJob", "结清证明下载跑批-绿信"),
    repayPlanOverdueDueBatch("repayPlanOverdueDueBatch", "还款计划逾期处理跑批"),
    loanAgreementJob("loanAgreementJob", "协议文件上传-绿信"),


    ;
    private final String code;
    private final String desc;
    JobEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // Getter
    public String getCode() { return code; }
    public String getDesc() { return desc; }

    public static List<String> getAllCodes() {
        return Arrays.stream(JobEnums.values())
            .map(JobEnums::getCode)
            .collect(Collectors.toList());
    }

}

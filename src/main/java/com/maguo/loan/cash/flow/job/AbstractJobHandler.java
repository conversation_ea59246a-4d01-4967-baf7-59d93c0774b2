package com.maguo.loan.cash.flow.job;


import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * <AUTHOR>
 * @date 2023/6/5
 */
public abstract class AbstractJobHandler extends IJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(AbstractJobHandler.class);

    @Override
    public ReturnT<String> execute(String param) {
        try {
            JobParam jobParam = null;
            // job指定
            if (StringUtil.isNotBlank(param)) {
                jobParam = JsonUtil.convertToObject(param, JobParam.class);
            }
            doJob(jobParam);

        } catch (Exception ex) {
            logger.error("job执行错误:", ex);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    public abstract void doJob(JobParam jobParam) throws Exception;
}

package com.maguo.loan.cash.flow.job.agreement;


import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.annotation.AlarmAnno;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.service.UserFileService;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 结清证明下载
 * @Date 2024/8/5 16:28
 **/
@Component
@JobHandler("clearVoucherDownloanJob")
public class ClearVoucherDownloanJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(LoanAgreementJob.class);
    private static final int SEVEN = 7;

    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private UserFileService userFileService;


    @Override
    @AlarmAnno(taskHandler="clearVoucherDownloanJob",taskDescription="结清证明下载跑批-绿信")
    public void doJob(JobParam jobParam) {
        logger.info("clearVoucherApplyJob jobParam:{}", JsonUtil.toJsonString(jobParam));
        if (Objects.isNull(jobParam)) {
            jobParam = new JobParam();
        }
        List<Loan> loanList;
        if (CollectionUtils.isEmpty(jobParam.getLoanIds())) {
            //查7天内结清的
            LocalDateTime startTime = getStartTime(jobParam.getStartDate());
            LocalDateTime endTime = getEndTime(jobParam.getEndDate(), jobParam.getStartDate());
            logger.info("clearVoucherApplyJob startTime:{},endTime:{}",
                startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME), endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            loanList = loanRepository.findClearLoanByUpdateTimeBetweenAndFlowChannel(startTime, endTime, FlowChannel.LVXIN);
            jobParam.setStartDate(LocalDate.from(startTime));
        } else {
            loanList = loanRepository.findClearLoanByLoanIdInAndFlowChannel(jobParam.getLoanIds(), FlowChannel.LVXIN);
        }

        logger.info("clearVoucherApplyJob 申请结清证明的记录数:{}", loanList.size());
        if (CollectionUtils.isEmpty(loanList)) {
            return;
        }

        //按借据单个下载
        loanList.forEach(loan -> userFileService.downloadByFileType(loan, FileType.CREDIT_SETTLE_VOUCHER_FILE));
    }

    private static LocalDateTime getStartTime(LocalDate startDate) {
        if (Objects.isNull(startDate)) {
            return LocalDate.now().minusDays(SEVEN).atStartOfDay();
        } else {
            return startDate.atStartOfDay();
        }
    }

    private static LocalDateTime getEndTime(LocalDate endDate, LocalDate startDate) {
        if (Objects.nonNull(endDate)) {
            return endDate.plusDays(1).atStartOfDay();
        } else {
            if (Objects.nonNull(startDate)) {
                //按开始时间,取当天的
                return startDate.plusDays(1).atStartOfDay();
            } else {
                //截止到当天0点
                return LocalDate.now().atStartOfDay();
            }
        }
    }
}

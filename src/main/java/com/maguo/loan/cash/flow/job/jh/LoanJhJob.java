package com.maguo.loan.cash.flow.job.jh;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.annotation.AlarmAnno;
import com.maguo.loan.cash.flow.config.LvXinConfig;
import com.maguo.loan.cash.flow.config.LvXinNewSFTPConfig;
import com.maguo.loan.cash.flow.entity.vo.LoanVo;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.service.JHReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.job.jh.LoanJob
 * @作者 Mr.sandman
 * @时间 2025/05/29 15:48
 */
@Component
@JobHandler("loanJhJob")
public class LoanJhJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(LoanJhJob.class);

    @Autowired
    private JHReconService jhReconService;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private LvXinConfig lvXinConfig;
    @Autowired
    private LvXinNewSFTPConfig lvXinNewSFTPConfig;

    @Override
    @AlarmAnno(taskHandler="loanJhJob",taskDescription="放款明细文件-绿信")
    public void doJob( JobParam jobParam ) throws Exception {
        // 参数要传 {"channel":"LVXIN","bankChannel":"CYBK/HXBK"}
        logger.info("生成绿信借款明细csv开始");
        try {
            // 优先从 JobParam 获取 startDate，如果没有则使用当前日期减一天
            LocalDate localDate;
            if ( Objects.nonNull(jobParam) && jobParam.getStartDate() != null) {
                localDate = jobParam.getStartDate();
            } else {
                localDate = LocalDate.now().minusDays(1);
            }
            jobParam.setStartDate(localDate);
            String  yesterday = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = "loan_" + yesterday + ".csv";
            String okFileName = "loan_" + yesterday + ".ok";
            String remoteDir = null;
            String interestsDir =null;
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                remoteDir = lvXinConfig.getLoanSftpPath() + yesterday + "/";
                interestsDir = lvXinConfig.getIncludingEquitySftpPath() + yesterday + "/";

            } else if ( jobParam.getBankChannel() == BankChannel.HXBK ) {
                remoteDir = lvXinNewSFTPConfig.getLoanSftpPath() + yesterday + "/";
            }
            FlowChannel flowChannel = FlowChannel.getFlowChannel(jobParam.getChannel());
            logger.info("获取绿信放款文件数据 时间: {}, 流量渠道: {}, 资金渠道: {}", localDate, flowChannel, jobParam.getBankChannel());
            // 获取权益客户数据数据
            List<LoanVo> loanVos = jhReconService.getLoanDetailReconFile(localDate,flowChannel,jobParam.getBankChannel(), IsIncludingEquity.N);
            List<LoanVo> interestsLoanVos = jhReconService.getLoanDetailReconFile(localDate,flowChannel,jobParam.getBankChannel(), IsIncludingEquity.Y);

            // 生成内存中的csv文件 字节流
            ByteArrayOutputStream stream = generateCsvToStream(loanVos);
            // 生成内存中的csv文件 字节流
            ByteArrayOutputStream  interestsStream = generateCsvToStream(interestsLoanVos);

            // 上传到 SFTP 长银沿用之前的 湖消新增
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                sftpUtils.uploadStreamToSftp(stream, fileName, remoteDir);
                // 上传.ok文件
                sftpUtils.uploadStreamToSftp(new ByteArrayOutputStream(), okFileName, remoteDir);

                sftpUtils.uploadStreamToSftp(interestsStream, fileName, interestsDir);

                sftpUtils.uploadStreamToSftp(new ByteArrayOutputStream(), okFileName, interestsDir);

                logger.info("绿信-长银借款明细csv上传成功");
            } else if ( jobParam.getBankChannel() == BankChannel.HXBK ) {
                sftpUtils.uploadStreamToNewSftp(stream, fileName, remoteDir);
                // 上传.ok文件
                sftpUtils.uploadStreamToNewSftp(new ByteArrayOutputStream(), okFileName, remoteDir);
                logger.info("绿信-湖消借款明细csv上传成功");
            }

        } catch (Exception e) {
            logger.error("绿信借款明细csv上传失败:{} 资方渠道: {}", e, jobParam.getBankChannel());
            e.printStackTrace();
            throw e;
        }


    }


    private static ByteArrayOutputStream generateCsvToStream(List<LoanVo> data) throws IOException {
        String[] headers = {
            "对方业务号", "客户编号", "用款申请日期", "用款申请流水号", "合同号", "借据号",
            "放款金额", "申请期限", "年化利率", "放款时间", "合同到期日", "放款账户姓名",
            "放款账户开户行", "放款账号"
        };

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
        BufferedWriter bw = new BufferedWriter(writer);

        // 写表头
        bw.write(String.join(",", headers));
        bw.newLine();

        // 写数据（你替换的逻辑）
        for (LoanVo loan : data) {
            bw.write(String.join(",", Arrays.asList(
                safe(loan.getOutAppSeq()), safe(loan.getCustId()), safe(loan.getApplyDt()),
                safe(loan.getApplSeq()), safe(loan.getContNo()), safe(loan.getLoanNo()),
                safe(loan.getDnAmt()), safe(loan.getApplyTnr().toString()), safe(loan.getBasicIntRat()),
                safe(loan.getLoanActvDt()), safe(loan.getContEndDt()), safe(loan.getAcctName()),
                safe(loan.getAcctBank()), safe(loan.getAcctNo()))));
            bw.newLine();
        }

        bw.flush();
        return outputStream;
    }

    // 处理 null 的字段
    private static String safe(String val) {
        return val == null ? "" : val;
    }

    private static String safe( BigDecimal val ) {
        return val == null ? "" : val.toPlainString();
    }

}

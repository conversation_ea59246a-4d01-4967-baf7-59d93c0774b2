package com.maguo.loan.cash.flow.job.jh;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.annotation.AlarmAnno;
import com.maguo.loan.cash.flow.config.LvXinConfig;
import com.maguo.loan.cash.flow.config.LvXinNewSFTPConfig;
import com.maguo.loan.cash.flow.entity.vo.RepayDetailVo;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.service.JHReconService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @公司 中数金智(上海)有限公司
 * @包名 com.maguo.loan.cash.flow.job.jh.RepayDetailJhJob
 * @作者 Mr.sandman
 * @时间 2025/05/29 17:19
 */
@Component
@JobHandler("repayDetailJhJob")
public class RepayDetailJhJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(RepayDetailJhJob.class);

    @Autowired
    private JHReconService jhReconService;
    @Autowired
    private SftpUtils sftpUtils;
    @Autowired
    private LvXinConfig lvXinConfig;
    @Autowired
    private LvXinNewSFTPConfig lvXinNewSFTPConfig;

    @Override
    @AlarmAnno(taskHandler="repayDetailJhJob",taskDescription="还款明细文件-绿信")
    public void doJob( JobParam jobParam ) throws Exception {
        // 参数要传 {"channel":"LVXIN","bankChannel":"CYBK/HXBK"}
        logger.info("生成绿信还款明细csv文件开始");
        try {
            // 优先从 JobParam 获取 startDate，如果没有则使用当前日期减一天
            LocalDate localDate;
            if ( Objects.nonNull(jobParam) && jobParam.getStartDate() != null) {
                localDate = jobParam.getStartDate();
            } else {
                localDate = LocalDate.now().minusDays(1);
            }
            jobParam.setStartDate(localDate);
            String  yesterday = localDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = "repay_" + yesterday + ".csv";
            String okFileName = "repay_" + yesterday + ".ok";
            String remoteDir = null;
            String includingDir =null;
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                remoteDir = lvXinConfig.getLoanSftpPath() + yesterday + "/";
                includingDir = lvXinConfig.getIncludingEquitySftpPath() + yesterday + "/";
            } else if ( jobParam.getBankChannel() == BankChannel.HXBK ) {
                remoteDir = lvXinNewSFTPConfig.getLoanSftpPath() + yesterday + "/";
            }
            FlowChannel flowChannel = FlowChannel.getFlowChannel(jobParam.getChannel());
            logger.info("获取绿信还款明细数据 时间: {}, 流量渠道: {}, 资金渠道: {}", localDate, flowChannel, jobParam.getBankChannel());
            // 获取数据
            List<RepayDetailVo> repayDetailVos = jhReconService.getRepayDetailReconFile(localDate,flowChannel,jobParam.getBankChannel(),IsIncludingEquity.N);
            List<RepayDetailVo> includingVos = jhReconService.getRepayDetailReconFile(localDate,flowChannel,jobParam.getBankChannel(), IsIncludingEquity.Y);

            // 生成csv文件流
            ByteArrayOutputStream stream = generateCsvToStream(repayDetailVos);
            ByteArrayOutputStream includingStream = generateCsvToStream(includingVos);

            // 上传到 SFTP 长银沿用之前的 湖消新增
            if ( jobParam.getBankChannel() == BankChannel.CYBK ) {
                sftpUtils.uploadStreamToSftp(stream, fileName, remoteDir);
                // 上传.ok文件
                sftpUtils.uploadStreamToSftp(new ByteArrayOutputStream(), okFileName, remoteDir);
                sftpUtils.uploadStreamToSftp(includingStream, fileName, includingDir);
                sftpUtils.uploadStreamToSftp(new ByteArrayOutputStream(), okFileName, includingDir);
                logger.info("绿信-长银还款明细csv上传成功");
            } else if ( jobParam.getBankChannel() == BankChannel.HXBK ) {
                sftpUtils.uploadStreamToNewSftp(stream, fileName, remoteDir);
                // 上传.ok文件
                sftpUtils.uploadStreamToNewSftp(new ByteArrayOutputStream(), okFileName, remoteDir);
                logger.info("绿信-湖消还款明细csv上传成功");
            }

        } catch ( Exception e ) {
            logger.error("绿信还款明细csv上传失败", e);
            e.printStackTrace();
            throw e;
        }
    }

    private static ByteArrayOutputStream generateCsvToStream(List<RepayDetailVo> data) throws IOException {
        String[] headers = {
            "借据号", "还款申请流水号", "记账流水号", "对方业务号", "客户编号", "还款模式",
            "实际还款日期", "实际还款时间", "是否扣款", "还款金额", "归还本金",
            "归还利息", "归还罚息", "归还复利", "咨询费", "还款后贷款余额",
            "是否代偿", "归还担保费", "归还担保费逾期费用", "商户订单号","违约金"
        };

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
        BufferedWriter bw = new BufferedWriter(writer);

        // 写表头
        bw.write(String.join(",", headers));
        bw.newLine();

        // 写数据
        for (RepayDetailVo loan : data) {
            bw.write(String.join(",", Arrays.asList(
                safe(loan.getLoanNo()), safe(loan.getRepaymentSeq()), safe(loan.getSetlSeq()),
                safe(loan.getOutApplSeq()), safe(loan.getCustId()), safe(loan.getPayMode()),
                safe(loan.getSetlDt()), safe(loan.getSetlTime()), safe(loan.getIsCy()),
                safe(loan.getTotalAmt()), safe(loan.getPrcpAmt()), safe(loan.getIntAmt()),
                safe(loan.getOdIntAmt()), safe(loan.getCommOdIntAmt()), safe(loan.getConsultFee()),
                safe(loan.getCurrPrincipal()), safe(loan.getIsDc()), safe(loan.getGuaranteeFeeAmt()),
                safe(loan.getGuaranteeFeeOdAmt()), safe(loan.getPlatformFlowNo()), safe(loan.getBreachAmt()))));
            bw.newLine();
        }

        bw.flush();
        return outputStream;
    }


    // 处理 null 的字段
    private static String safe(String val) {
        return val == null ? "" : val;
    }

    private static String safe( BigDecimal val ) {
        return val == null ? "" : val.toPlainString();
    }

}

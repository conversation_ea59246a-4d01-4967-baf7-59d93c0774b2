package com.maguo.loan.cash.flow.job.ppd;

import com.alibaba.fastjson2.JSON;
import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.Product;
import com.jinghang.capital.api.dto.RestResult;
import com.jinghang.capital.api.dto.file.FileDownloadDto;
import com.jinghang.capital.api.dto.file.FileDownloadResultDto;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.annotation.AlarmAnno;
import com.maguo.loan.cash.flow.config.LvXinConfig;
import com.maguo.loan.cash.flow.convert.EnumConvert;
import com.maguo.loan.cash.flow.entity.AgreementSignRelation;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entrance.ppd.config.PpdConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.LoanStage;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.QhBank;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.remote.core.FinLoanFileService;
import com.maguo.loan.cash.flow.repository.AgreementSignRelationRepository;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.util.SftpUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @作者 Mr.sandman
 * @时间 2025/06/26 16:51
 */
@Component
@JobHandler(value = "ppdSignJob")
public class PPDSignJob extends AbstractJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PPDSignJob.class);

    public static final Map<QhBank, List<FileType>> AGREEMENT_MAP = new HashMap<>();


    @Autowired
    private SftpUtils sftpUtils;

    @Autowired
    private PpdConfig ppdConfig;

    @Autowired
    private OrderRepository orderRepository;


    static {

        // 拍拍-长银
        List<FileType> cybkList =
            List.of(
                FileType.LOAN_CONTRACT,
                FileType.SYNTHESIS_AUTHORIZATION,
                FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER,
                FileType.ENTRUSTED_DEDUCTION_LETTER
                   );
        AGREEMENT_MAP.put(QhBank.CYBK, cybkList);
    }

    static {
        // 拍拍-湖消
        List<FileType> hxbkList =
            List.of(
                FileType.LOAN_CONTRACT,
                FileType.PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT,
                FileType.ARBITRATION_AGREEMENT
                   );
        AGREEMENT_MAP.put(QhBank.HXBK, hxbkList);
    }

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private FinLoanFileService finLoanFileService;

    @Autowired
    private WarningService warningService;

    @Autowired
    private UserFileRepository userFileRepository;

    @Autowired
    private AgreementSignRelationRepository agreementSignRelationRepository;

    @Value(value = "${oss.bucket.name}")
    private String ossBucket;

    @Autowired
    private FileService fileService;

    @Autowired
    private CreditRepository creditRepository;

    @Override
    @AlarmAnno(taskHandler="ppdSignJob",taskDescription="协议文件上传-拍拍")
    public void doJob(JobParam jobParam) {
        logger.info("ppdSignJob jobParam:{}", JsonUtil.toJsonString(jobParam));

        LocalDate fileDate = jobParam.getStartDate();
        LocalDate endDate = jobParam.getEndDate();
        if ( Objects.isNull(fileDate)) {
            fileDate = LocalDate.now().minusDays(1);
        }
        if (Objects.isNull(endDate)) {
            endDate = fileDate;
        }
        jobParam.setStartDate(fileDate);

        List<Loan> list;
        if ( CollectionUtil.isNotEmpty(jobParam.getLoanIds())) {
            logger.info("ppdSignJob loanIds size:{}", jobParam.getLoanIds().size());
            //根据借据id数组查询
            list = loanRepository.findAllById(jobParam.getLoanIds());
        } else if (jobParam.getBankChannel() != null) {
            if (jobParam.getStartDate() == null || jobParam.getEndDate() == null) {
                logger.error("ppdSignJob 指定资方,startDate和endDate必填");
                return;
            }
            logger.info("ppdSignJob 指定资方:{},开始时间:{},结束时间:{}", jobParam.getBankChannel(), jobParam.getStartDate(), jobParam.getEndDate());
            //根据资方、放款时间 查询
            list = loanRepository.findByLoanStateAndBankChannelAndLoanTimeBetweenAndFlowChannel(
                ProcessState.SUCCEED, jobParam.getBankChannel(), LocalDateTime.of(fileDate, LocalTime.MIN), LocalDateTime.of(endDate, LocalTime.MAX), FlowChannel.PPCJDL);
        } else {
            logger.info("ppdSignJob 指定开始时间:{},结束时间:{}", fileDate, endDate);
            list = loanRepository.findByLoanStateAndLoanTimeBetweenAndFlowChannel(
                ProcessState.SUCCEED, LocalDateTime.of(fileDate, LocalTime.MIN), LocalDateTime.of(endDate, LocalTime.MAX), FlowChannel.PPCJDL);
        }

        if ( CollectionUtils.isEmpty(list)) {
            logger.info("ppdSignJob 放款成功的借据为空");
            return;
        }

        // 开始拉取
        logger.info("借款合同下载,loan size:{}", list.size());
        for (Loan loan : list) {
            logger.info("开始获取协议loanId:{}", loan.getId());
            List<UserFile> userFiles = agreementSignRelationRepository.queryUserFiles(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
            Map<String, String> ossMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(userFiles)) {
                ossMap = userFiles.stream().collect(Collectors.toMap(UserFile::getOssKey, UserFile::getOssBucket, ( k1, k2) -> k2));
            }
            List<FileType> fileTypes = AGREEMENT_MAP.get(QhBank.getQhBankBy(loan.getBankChannel()));
            for (FileType fileType : fileTypes) {
                try {
                    //下载借款合同
                    FileDownloadDto fileDownloadDto = new FileDownloadDto();
                    fileDownloadDto.setLoanId(loan.getLoanNo());
                    fileDownloadDto.setLoanOrderId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
                    fileDownloadDto.setType(fileType);
                    fileDownloadDto.setProduct(Product.ZC_CASH);
                    fileDownloadDto.setBankChannel(loan.getBankChannel());
                    RestResult<FileDownloadResultDto> restResult = finLoanFileService.download(fileDownloadDto);
                    logger.info("下载借款合同文件,入参{},出参{}", JSON.toJSONString(fileDownloadDto), JSON.toJSONString(restResult));
                    if (!restResult.isSuccess()) {
                        //warningService.warn("下载借款合同文件异常:" + JSON.toJSONString(fileDownloadDto),
                        //    msg -> logger.error("下借款合同文件异常:{}", JSON.toJSONString(restResult)));
                        logger.error("下借款合同文件异常,loanId:{}", loan.getId());
                        continue;
                    }
                    if (Objects.isNull(restResult.getData()) || StringUtils.isAnyBlank(restResult.getData().getOssBucket(),
                                                                                       restResult.getData().getOssPath())) {
                        logger.info("合同文件不存在,loanId:{},fileType:{}", loan.getId(), fileType);
                        continue;
                    }
                    // 过滤已经下载的文件
                    if (ossMap.containsKey(restResult.getData().getOssPath()) && ossMap.get(restResult.getData().getOssPath())
                        .equals(restResult.getData().getOssBucket())) {
                        logger.info("借据:{},跳过:{}文件下载", loan.getId(), fileType);
                        continue;
                    }
                    //新增影像协议文件
                    saveUserFile(loan, fileType, restResult);


                } catch (Exception e) {
                    logger.error("借款合同下载异常loanId:{}", loan.getId(), e);
                }
            }


            Order order = orderRepository.findOrderById(loan.getOrderId());
            Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();
            List<UserFile> byUserId = userFileRepository.findByUserId(order.getUserId());
            logger.info("合同文件数量:{}, 内容如下:", byUserId.size());

            for (UserFile userFile : byUserId) {
                if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.ID_HEAD) ||
                    userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.ID_NATION) ||
                    userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.ID_FACE)) {
                    continue;
                }
                logger.info(" - fileId:{}, fileType:{}, fileName:{}, ossKey:{}",
                            userFile.getId(), userFile.getFileType(), userFile.getFileName(), userFile.getOssKey());
                fileService.getOssFile(userFile.getOssBucket(), userFile.getOssKey(), inputStream -> {
                    try {
                        /**
                         * 《个人敏感信息授权书-修订》 comprehensiveAuthorizationLetterGuarantee_01.pdf
                         *   《 综合授权书-担保(四合一) 》    personalInformationAuthorizationLetter_02.pdf
                         *   《  咨询服务合同(超捷)定稿》    consultingServiceContract_03.pdf
                         *     《    承诺书》        letterOfCommitment_04.pdf
                         *     《  委托扣款授权书-担保vs借款人》  authorizationLetterForEntrustedDeductionGuarantee_05.pdf
                         *     《   综合授权书(通用)》         synthesisAuthorization_06.pdf
                         *      《数字证书使用授权协议》   digitalCertificateAuthorizationLetter_07.pdf
                         *      《 个人客户扣款授权书》          entrustedDeductionLetter_08.pdf
                         *     《  借款合同及贷款告知事项客户声明书》  loanContract_09.pdf
                         */
                        String fileName;
                        String remoteDir;
                        if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.LOAN_CONTRACT)) {
                            remoteDir = generateLoanTimeFilePath(loan);
                            fileName = loan.getOuterLoanId() + "_dkht.pdf";
                        } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.ENTRUSTED_DEDUCTION_LETTER)) {
                            remoteDir = generateLoanTimeFilePath(loan);
                            fileName = loan.getOuterLoanId() + "_kksqs_zf.pdf";
                        } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER)) {
                            remoteDir = generateLoanTimeFilePath(loan);
                            fileName = loan.getOuterLoanId() + "_szzssqs.pdf";
                        } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.SYNTHESIS_AUTHORIZATION)) {
                            remoteDir = generateLoanTimeFilePath(loan);
                            fileName = loan.getOuterLoanId() + "_zhsqs.pdf";
                        } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE)) {
                            remoteDir = generateLoanTimeFilePath(loan);
                            fileName = loan.getOuterLoanId() + "_kksqs_rd.pdf";
                        } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.LETTER_OF_COMMITMENT)) {
                            remoteDir = generateLoanTimeFilePath(loan);
                            fileName = loan.getOuterLoanId() + "_fxscnh.pdf";
                        } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.CONSULTING_SERVICE_CONTRACT)) {
                            remoteDir = generateLoanTimeFilePath(loan);
                            fileName = loan.getOuterLoanId() + "_dbzxfxy.pdf";
                        } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER)) {
                            remoteDir = generateLoanTimeFilePath(loan);
                            fileName = loan.getOuterLoanId() + "_mgxxsqs.pdf";
                        } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE)) {
                            remoteDir = generateLoanTimeFilePath(loan);
                            fileName = loan.getOuterLoanId() + "_czsqs_zf.pdf";
                        }
                        //拍拍-湖消 需增加征信授权书与仲裁协议路径与名称
                        else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT)) {//征信授权书
                            remoteDir = generateLoanTimeFilePath(loan);
                            fileName = loan.getOuterLoanId() + "_zhsqs.pdf";
                            //拍拍-湖消 需增加征信授权书与仲裁协议路径与名称
                        } else if (userFile.getFileType().equals(com.maguo.loan.cash.flow.enums.FileType.ARBITRATION_AGREEMENT)) {//仲裁协议
                            remoteDir = generateLoanTimeFilePath(loan);
                            fileName = loan.getOuterLoanId() + "_dkhtbcxy.pdf";
                            //拍拍-湖消 需增加征信授权书与仲裁协议路径与名称
                        }else {
                            remoteDir = "/";
                            fileName = userFile.getFileType().name();
                        }
                        // 根据资方渠道区分不同的sftp账号
                        if (loan.getBankChannel() == BankChannel.CYBK) {
                            //CYBK
                            logger.info("上传文件——长银SFTP, fileName:{}, remotePath:{}", fileName, ppdConfig.getSftpDownloadPath() + remoteDir);
                            sftpUtils.uploadStreamToPPCJDLSftp(inputStream, fileName, ppdConfig.getSftpDownloadPath() + remoteDir);
                        } else if (loan.getBankChannel() == BankChannel.HXBK) {
                            //HYBK
                            logger.info("上传文件——湖消SFTP, fileName:{}, remotePath:{}", fileName, ppdConfig.getSftpHxDownloadPath() + remoteDir);
                            sftpUtils.uploadStreamToPPCJDLSftpHx(inputStream, fileName, ppdConfig.getSftpHxDownloadPath() + remoteDir);
                        }
                        logger.info("协议文件上传拍拍sftp成功");
                    } catch (Exception e) {
                        logger.error("协议文件上传拍拍sftp失败:", e);
                        throw new RuntimeException(e);
                    }
                });

            }


        }
        logger.info("PPDSignJob finished");
    }

    /**
     * 生成包含放款时间、外部借据ID的路径字符串
     *
     * @param loan 包含放款时间和外部借据ID的对象
     * @return 格式化后的字符串
     */
    private String generateLoanTimeFilePath(Loan loan) {
        if (loan == null || loan.getLoanTime() == null || StringUtils.isBlank(loan.getOuterLoanId())) {
            return "";
        }
        String formattedDate = formatLocalDateTime(loan.getLoanTime());
        String outerLoanId = loan.getOuterLoanId();
        logger.info("generateLoanTimeFilePath:{}", formattedDate + "/" + outerLoanId + "/");
        return formattedDate + "/" + outerLoanId + "/";
    }

    /**
     * 将 LocalDateTime 转换为格式为 "yyyyMMdd" 的字符串
     *
     * @param dateTime LocalDateTime 对象
     * @return 格式化后的字符串
     */
    private static String formatLocalDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return dateTime.format(formatter);
    }

    private void saveUserFile(Loan loan, FileType fileType, RestResult<FileDownloadResultDto> restResult) {
        FileDownloadResultDto resultData = restResult.getData();
        UserFile userFile = new UserFile();
        userFile.setUserId(loan.getUserId());
        LoanStage loanStage = getLoanStage(fileType);
        userFile.setLoanStage(loanStage);
        userFile.setLoanNo(loan.getId());
        com.maguo.loan.cash.flow.enums.FileType cashFileType = EnumConvert.INSTANCE.toCoreApi(fileType);
        userFile.setFileType(cashFileType);
        String fileName = StringUtils.isNotBlank(resultData.getFileName())
            ? resultData.getFileName() : Optional.ofNullable(cashFileType).map(com.maguo.loan.cash.flow.enums.FileType::getDesc).orElse("");
        userFile.setFileName("资金-" + fileName);
        userFile.setOssBucket(resultData.getOssBucket());
        userFile.setOssKey(resultData.getOssPath());
        userFile.setSignFinal(ProcessStatus.SUCCESS == resultData.getFileStatus() ? WhetherState.Y : WhetherState.N);
        userFileRepository.save(userFile);

        AgreementSignRelation agreementSignRelation = new AgreementSignRelation();
        agreementSignRelation.setLoanStage(loanStage);
        agreementSignRelation.setSignApplyId(userFile.getId());
        agreementSignRelation.setUserId(loan.getUserId());
        agreementSignRelation.setOrderId(loan.getOrderId());
        if (loanStage == LoanStage.CREDIT) {
            agreementSignRelation.setRelatedId(loan.getCreditId());
        } else {
            agreementSignRelation.setRelatedId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
        }
        agreementSignRelationRepository.save(agreementSignRelation);
    }

    private LoanStage getLoanStage(FileType fileType) {
        return switch (fileType) {
            case CREDIT_APPLY, PERSONAL_CREDIT_AUTHORIZATION_LETTER, PROMISE_NOT_STUDENT,
                PERSONAL_INFORMATION_QUERY_LETTER -> LoanStage.CREDIT;
            default -> LoanStage.LOAN;
        };
    }
}

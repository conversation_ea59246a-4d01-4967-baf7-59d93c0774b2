package com.maguo.loan.cash.flow.job.state;

import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.entity.BatchTaskMonitoring;
import com.maguo.loan.cash.flow.enums.JobEnums;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.BatchTaskMonitoringRepository;
import com.maguo.loan.cash.flow.service.WarningService;
import com.xxl.job.core.handler.annotation.JobHandler;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@JobHandler("taskMonitoringJob")
public class TaskMonitoringJob extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(TaskMonitoringJob.class);

    private final Map<String, String> jobHandlerMap;

    @Autowired
    private BatchTaskMonitoringRepository batchTaskMonitoringRepository;

    @Resource(name = "warningStateService")
    private WarningService warningService;

    public TaskMonitoringJob() {
        this.jobHandlerMap =
//            JobEnums.getAllCodes().stream()
//            .collect(Collectors.toMap(
//                s -> s,
//                s -> s
//            ));

         Arrays.stream(JobEnums.values())
            .collect(Collectors.toMap(
                JobEnums::name,  // 使用枚举的 name() 方法
                JobEnums::getDesc
            ));
    }

    @Override
    public void doJob(JobParam jobParam) {
        try {
            logger.info("开始执行任务监控检查, 参数: {}", JsonUtil.toJsonString(jobParam));

            LocalDate checkDate = getCheckDate(jobParam);
            checkUncompletedTasks(checkDate,jobParam.getTaskHandler());

            logger.info("任务监控检查完成");
        } catch (Exception e) {
            logger.error("任务监控检查异常", e);
            throw e;
        }
    }

    private LocalDate getCheckDate(JobParam jobParam) {
        return jobParam == null || jobParam.getStartDate() == null
            ? LocalDate.now().minusDays(1)
            : jobParam.getStartDate();
    }

    private void checkUncompletedTasks(LocalDate date,String taskHandler) {
        // 创建副本以避免修改原始map
        Map<String, String> remainingJobs = new HashMap<>(jobHandlerMap);
        List<BatchTaskMonitoring> completedTasks =new ArrayList<>();
        if(StringUtils.isEmpty(taskHandler)){
            completedTasks= batchTaskMonitoringRepository.findBatchTaskData(
                date, ProcessState.SUCCEED.name());
        }else {
            //跑罚息任务
            completedTasks= batchTaskMonitoringRepository.findBatchTaskHandler(
                LocalDate.now(), ProcessState.SUCCEED.name(),taskHandler);
        }

        if (!CollectionUtil.isEmpty(completedTasks)&&StringUtils.isEmpty(taskHandler)) {
            completedTasks.forEach(task -> remainingJobs.remove(task.getTaskHandler()+task.getBankChannel()));
        }
        String warningMessage =null;
        //跑罚息任务
        if (!StringUtils.isEmpty(taskHandler) && ObjectUtils.isEmpty(completedTasks)) {
            warningMessage=String.format("%s：跑批任务：%s，未执行，请尽快处理", date, JsonUtil.toJsonString(taskHandler));
            logger.warn(warningMessage);
        } else if (!remainingJobs.isEmpty()) {
             warningMessage = String.format("%s：跑批任务：%s，未执行，请尽快处理", date, JsonUtil.toJsonString(remainingJobs));
            logger.warn(warningMessage);
        } else {
            logger.info("所有任务已正常完成");
        }

        if(!StringUtils.isEmpty(warningMessage)){
            warningService.warn(warningMessage);
        }
    }
}

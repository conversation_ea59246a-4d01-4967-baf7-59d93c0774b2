package com.maguo.loan.cash.flow.repository;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.dto.ProcessDTO;
import com.maguo.loan.cash.flow.entity.CapitalConfig;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import groovy.lang.Tuple;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * 资金配置
 */
public interface CapitalConfigRepository extends JpaRepository<CapitalConfig, String> {

    Optional<CapitalConfig> findByBankChannel(BankChannel bankChannel);

//    @Query("select fc.flowChannel as flowChannel,cc.bankChannel as bankChannel from CapitalConfig cc\n" +
//        "left join FlowRouteConfig rc on cc.id=rc.capitalId\n" +
//        "left join FlowConfig fc on rc.flowId=fc.id\n" +
//        "where cc.creditStartTime< ?1")
//    List<ProcessDTO> findByTime(String time);
    @Query(value = """
    SELECT fc.flow_channel as flowChannel,
           cc.bank_channel as bankChannel
    FROM capital_config cc
    LEFT JOIN flow_route_config rc ON cc.id = rc.capital_id
    LEFT JOIN flow_config fc ON rc.flow_id = fc.id
    WHERE cc.credit_start_time < ?1""",
        nativeQuery = true)
    List<Map<String, Object>> findByTime(String time);

}
